tenant_columns:
- fs_ea
- ea
- enterprise_account
- ei
- tenant_id
- enterprise_id
- corp_id
- fs_corp_id
- source_corp_id
- upstream_fs_ea
- upstream_ea
- downstream_ea
- downstream_fs_ea
- out_ea
- out_fs_ea
- out_tenant_id
- out_enterprise_id
ea_alias:
- fs_ea
- ea
- enterprise_account
- enterpriseAccount
- EA
- upstream_ea
- downstream_ea
- source_ea
- fsEa
ei_alias:
- ei
- EI
- tenant_id
update_columns:
- gmt_create
- gmt_modified
- time_create
- time_modified
- create_time
- update_time
- create_at
- update_at
- modify_at
- create_date
- update_date
- modify_date
- modify_time
include_names:
- account_book_config
- account_book_user
- cases_checkins_scene_config_match_result
- current_service_app
- custom_menu_bind
- customer_crm_info
- customer_quick_reply
- customer_quick_reply_class
- customer_quick_reply_version
- customer_rule_setting
- customer_session_info
- customer_transfer_info
- employee_status
- eservice_agent_config
- eservice_biz_config
- eservice_bot_config
- eservice_bot_mapping
- eservice_bpm_app_action_task
- eservice_busy_idle_rule
- eservice_check_group_data
- eservice_check_group_task
- eservice_checkins_sync_data
- eservice_code_rule
- eservice_config
- eservice_custom_component_register
- eservice_custom_status_config
- eservice_dispatcher_filter
- eservice_dispatcher_scope
- eservice_email_template_config
- eservice_file_slice_info
- eservice_gnomon_task
- eservice_knowledge_category_custom_order
- eservice_knowledge_external_config
- eservice_knowledge_role
- eservice_knowledge_scene_config
- eservice_menu_record
- eservice_mini_program_config
- eservice_nearby_work_config
- eservice_obj_relation
- eservice_quick_qrcode
- eservice_qywx_notify
- eservice_retry
- eservice_return_visit
- eservice_return_visit_rule
- eservice_rma_express_config
- eservice_sales_order_create_rule
- eservice_search_tool
- eservice_service_agreement_auto_create_rule
- eservice_service_plan_rule_scope
- eservice_short_message_template_config
- eservice_skill_verify_rule
- eservice_sync_record
- eservice_tencent_meeting_mapping
- eservice_terminal_user_action_config
- eservice_terminal_user_action_task
- gray_application_group_config
- gray_application_info
- inspection_config
- inspection_status_config
- knowledge_config
- open_customer
- open_customer_expert
- open_customer_group
- outer_service_crm_role
- outer_service_wechat
- service_app_node_action_config
- service_fault_generate_rule
- service_knowledge_helpful_record
- service_management_accept_order
- service_management_assign_work_order
- service_management_base_info
- service_management_bpm_app_notify
- service_management_cases_accessory_remind_config
- service_management_cases_checkins_scene_config
- service_management_checkins_cases_setting
- service_management_checkins_order
- service_management_custom_field_list_config
- service_management_dispatch_rule
- service_management_dispatch_work_order
- service_management_downstream_assign_work_order
- service_management_downstream_config
- service_management_downstream_submit_workorder_config
- service_management_eservice_reset_record
- service_management_external_apply_task
- service_management_fee_settlement_config
- service_management_fee_settlement_rule
- service_management_field_mapping_config
- service_management_modify_record_list
- service_management_nobind_outer
- service_management_object_workflow_reference
- service_management_preview_template
- service_management_probation_config
- service_management_rate_order
- service_management_short_message_record
- service_management_short_message_template
- service_management_work_order
- service_management_work_order_filter
- service_management_work_service_group
- service_management_work_task
- service_management_workflow
- service_management_workflow_config
- service_management_workflow_exception_notice
- service_management_workflow_notice_template
- service_management_workflow_short_message_template
- service_management_workflow_task_config
- service_management_workflow_task_reference
- service_management_workflow_wechat_notice_template
- service_management_workrate_config
- service_plan_type_config
- service_report_template
- service_request_assign_rule
- service_request_bpm_action_task
- service_request_config
- service_request_consultant_info
- service_request_department_group
- service_request_group_info
- service_request_work_flow_config
- sla_function
- sla_function_task
- sla_remind
- sla_remind_record
- sla_remind_task
- sla_rule
- sla_rule_detail
- template_message_config
- wechat_fan_base_info
- wechat_fan_extra
- wechat_fan_tag
- wechat_fan_tag_detail
include_names_regex:
exclude_names:
exclude_names_regex:
- ^(?:test_|cctrl_|flyway|backup_|bak_|my_|v_|f_|buf_|junit|unit_|fs_eye|fs-eye|Test|xxl|eolink|cypress|sch_|datax)
- (?:_old|-test|_test|_unread|_tmp|_temp|_backup|_copy|_bak|wordpress|jacoco|_\d{4,})$
- _bak_|__del_
- ^(?:zhaow|liuj|lir|zhaowz|ggt|yanlian|pg_|spatial_ref_sys)
- ^(?:admin|local|test|config)

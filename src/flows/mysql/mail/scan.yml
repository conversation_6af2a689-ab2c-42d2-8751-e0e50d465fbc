tenant_columns:
- fs_ea
- ea
- enterprise_account
- enterprise_id
- ei
- tenant_id
- fs_corp_id
- source_corp_id
ea_alias:
- fs_ea
- ea
- enterprise_account
- enterpriseAccount
- EA
- upstream_ea
- downstream_ea
- source_ea
- fsEa
ei_alias:
- ei
- EI
- tenant_id
update_columns:
- gmt_create
- gmt_modified
- time_create
- time_modified
- create_time
- update_time
- create_at
- update_at
- modify_at
- create_date
- update_date
- modify_date
- modify_time
include_names:
- email
- email_activity
- email_config
- email_setting
- enterprise_email_config
- folder
- operation
- share_message
- share_message_content
- crm_email_bind_1
- crm_email_bind_2
- crm_email_bind_3
- crm_email_bind_4
- crm_email_bind_5
- crm_email_bind_6
- crm_email_bind_7
- crm_email_bind_8
- crm_email_bind_9
- crm_email_bind_10
- email_contracts_1
- email_contracts_2
- email_contracts_3
- email_contracts_4
- email_contracts_5
- email_contracts_6
- email_contracts_7
- email_contracts_8
- email_contracts_9
- email_contracts_10
- message_1
- message_2
- message_3
- message_4
- message_5
- message_6
- message_7
- message_8
- message_9
- message_10
- message_content_1
- message_content_2
- message_content_3
- message_content_4
- message_content_5
- message_content_6
- message_content_7
- message_content_8
- message_content_9
- message_content_10
include_names_regex:
exclude_names:
exclude_names_regex:
- ^(?:test_|cctrl_|flyway|backup_|bak_|my_|v_|f_|buf_|junit|unit_|fs_eye|fs-eye|Test|xxl|eolink|cypress|sch_|datax)
- (?:_old|-test|_test|_unread|_tmp|_temp|_backup|_copy|_bak|wordpress|jacoco|_\d{4,})$
- _bak_|__del_
- ^(?:zhaow|liuj|lir|zhaowz|ggt|yanlian|pg_|spatial_ref_sys)
- ^(?:admin|local|test|config)
- crm_email_relation_\d+

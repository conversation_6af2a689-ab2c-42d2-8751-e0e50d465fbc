# fs-prefect-flows


## install prefect
```bash
pip install --upgrade prefect
```

## set profile

```bash
prefect config set PREFECT_API_URL="https://prefect.foneshare.cn/api"
```

## run demo

```python
from prefect import flow, task


@task
def echo(who: str):
    return "hi " + who


@flow(log_prints=True)
def parallel(names):
    for p in names:
        print(f"你好, {p}")
        echo.submit(p)


parallel(['lir', 'colin'])
```

```bash
python demo.py
```

```text
22:50:53.777 | INFO    | prefect.engine - Created flow run 'silver-raccoon' for flow 'parallel'
22:50:53.778 | INFO    | Flow run 'silver-raccoon' - View at https://prefect.foneshare.cn/flow-runs/flow-run/ac50bedb-420e-4815-ac74-d937f4690adf
22:50:53.837 | INFO    | Flow run 'silver-raccoon' - 你好, lir
22:50:53.857 | INFO    | Flow run 'silver-raccoon' - 你好, colin
22:50:53.901 | INFO    | Flow run 'silver-raccoon' - Created task run 'echo-0' for task 'echo'
22:50:53.902 | INFO    | Flow run 'silver-raccoon' - Submitted task run 'echo-0' for execution.
22:50:53.905 | INFO    | Flow run 'silver-raccoon' - Created task run 'echo-1' for task 'echo'
22:50:53.905 | INFO    | Flow run 'silver-raccoon' - Submitted task run 'echo-1' for execution.
22:50:54.034 | INFO    | Task run 'echo-0' - Finished in state Completed()
22:50:54.046 | INFO    | Task run 'echo-1' - Finished in state Completed()
22:50:54.087 | INFO    | Flow run 'silver-raccoon' - Finished in state Completed('All states completed.')
```

## 主要文件说明
### db_url.txt
每行一个数据库连接信息，格式为：`<name> <base64_encode_password> <db_url>`

```text
paas_template   YWJjZDEyMzRQV0Q=   mysql://fs-paas-temp@172.17.22.221:3306/FS-PAAS-TEMPLATE-DB
paas_template_dst   YmRjYTEyMzRQV0Q=    mysql://test_mysql_admin@172.17.0.96:3306/FS-PAAS-TEMPLATE-DB
paas_crm_biz    QUJDRDEyMzRwd2Q=    mysql://fs-crm-biz@172.17.22.121:3306/FS-CRM-BIZ-DB
paas_crm_biz_dst    YWJjZDQ1NjdQV0Q=    mysql://test_mysql_admin@172.17.0.96:3306/FS-CRM-BIZ-DB
```

### prepare.py
根据`db_url.txt`中的数据库连接信息，调用 RestAPI 生成`prefect`的`variables`和`secrets`

```bash
python prepare.py
```
到 https://prefect.foneshare.cn/variables/ 查看生成的`variables`。
到 https://prefect.foneshare.cn/blocks/ 查看生成的`secrets`。

### deploy.py
根据配置生成相关的`flow`和`deployment`，并部署到 Prefect Server。

```bash
python deploy-mysql.py
```
到 https://prefect.foneshare.cn/flows/ 查看生成的`flow`。
到 https://prefect.foneshare.cn/deployments/ 查看生成的`deployment`。


# -*- coding: utf-8 -*-

import asyncio
import hashlib
import os
import re
import pandas as pd
import yaml
from datetime import datetime
from typing import Dict, List, Tuple, Optional

from prefect import flow, get_run_logger, tags, task
from prefect.artifacts import create_markdown_artifact, create_table_artifact
from prefect_sqlalchemy import SqlAlchemyConnector

from flows.bean import TableFilters
from flows.tool.conversion import ei_to_ea
from flows.mysql.scan import build_query_template, mysql_scan_tables, mysql_get_tenant_columns, mysql_get_update_columns


def match_tables_with_patterns(all_tables: List[str], table_patterns: List[Dict]) -> Tuple[List[Dict], List[str]]:
    """
    根据通配符模式匹配实际表名，同一个表可能有多个查询条件。
    
    Args:
        all_tables: 数据库中所有表的列表
        table_patterns: 包含通配符的表配置列表
        
    Returns:
        List[Dict]: 匹配后的表信息列表，每个表都包含实际表名和配置信息
        List[str]: 新发现的表，未匹配的表名列表，需要识别租户字段和更新字段
    """
    matched_tables = []
    hit_names = set()
    wildcard_patterns = []
    for table_pattern in table_patterns:
        table_name = table_pattern['name']
        # 如果模式包含通配符，则进行匹配
        if '*' in table_name:
            wildcard_patterns.append(table_pattern)
        elif table_name in all_tables:
            hit_names.add(table_name)
            matched_tables.append(table_pattern)
        else:
            get_run_logger().warning(f"Pattern '{table_name}' not found in database")

    for table_pattern in wildcard_patterns:
        table_name = table_pattern['name']
        pattern = table_name.replace('*', r'\d+')
        matched_names = [t for t in all_tables if re.match(pattern, t) and t not in hit_names]
        for matched_name in matched_names:
            hit_names.add(matched_name)
            # 为每个匹配的表创建一个新的配置
            table_info = table_pattern.copy()
            table_info['name'] = matched_name
            table_info['pattern'] = table_name  # 保留原始模式用于调试
            matched_tables.append(table_info)
            print(f"Pattern '{pattern}' matched table: {matched_name}")
    
    print(f"Total matched {len(matched_tables)} table patterns")
    
    # 未匹配的表
    missed_tables = [t for t in all_tables if t not in hit_names]
    if len(missed_tables) > 0:
        get_run_logger().warning(f"found missed tables: {missed_tables}")
    return matched_tables, missed_tables


@task(name='count-table-rows', log_prints=True, tags=['mysql', 'count'])
async def count_table_rows(db_blk_name: str, table_info: Dict, ei: int, update_time: Optional[str] = None) -> Tuple[str, int, str, str]:
    """
    异步统计单个表的行数
    
    Args:
        db_blk_name: 数据库连接块名称
        table_info: 表信息字典，包含表名、字段类型等
        ei: 企业ID
        update_time: 更新时间过滤器，格式为 '2024-01-01'
        
    Returns:
        Tuple[str, int, str, str]: (表名, 行数, 查询条件, 查询哈希)
    """
    table_name = table_info['name']
    try:
        # Build query condition
        condition = build_query_condition(table_info, ei, update_time)
        
        # Calculate query hash
        query_template = table_info.get('query', '')
        query_hash = hashlib.md5(query_template.encode('utf-8')).hexdigest()[:8]
        
        # Execute query using SqlAlchemyConnector
        connector = await SqlAlchemyConnector.load(db_blk_name) # type: ignore
        async with connector as conn:
            if condition:
                sql = f"SELECT COUNT(*) FROM `{table_name}` WHERE {condition}"
            else:
                sql = f"SELECT COUNT(*) FROM `{table_name}`"
            print(f"Executing query: {sql}")
            result = await conn.fetch_one(sql) # type: ignore
            count = result[0] if result else 0
        
        print(f"Table {table_name} count result: {count}, query_hash: {query_hash}, query condition: {condition}")
        return table_name, count, condition, query_hash
    except Exception as e:
        get_run_logger().error(f"Error counting rows in table {table_name}: {str(e)}")
        # Calculate query hash even for failed queries
        query_template = table_info.get('query', '')
        query_hash = hashlib.md5(query_template.encode('utf-8')).hexdigest()[:8]
        return table_name, -1, "", query_hash


def build_query_condition(table_info: Dict, ei: int, update_time: Optional[str] = None) -> str:
    """
    根据表信息构建查询条件
    
    Args:
        table_info: 表信息字典
        ei: 企业ID
        update_time: 更新时间，格式为 '2024-01-01'
        
    Returns:
        str: 构建的查询条件
    """
    table_name = table_info['name']
    template = table_info['query']
    tenant_type = table_info.get('tenant_type', '')
    if not template:
        get_run_logger().warning(f"cannot found query template in {table_name}")
        return ''
    query = template
    quote = lambda x, kind: "'" + x + "'" if kind == 'string' else x
    # todo: change later
    ei_to_ea = lambda x: 'fs'
    if any(x in template for x in (r'$ea', r'${ea}')):
        ea = quote(ei_to_ea(ei), tenant_type)
        query = re.sub(r'\$ea|\${ea}', ea, query)
    if any(x in template for x in (r'$ei', r'${ei}')):
        ei2 = quote(ei, tenant_type)
        query = re.sub(r'\$ei|\${ei}', str(ei2), query)

    # append time filters
    update_column = table_info.get('update_column', '')
    update_type = table_info.get('update_type', '')
    if not update_time or not update_column:
        return query
    time_filter = ''
    if update_type == 'datetime':
        time_filter = f"`{update_column}` >= '{update_time} 00:00:00'"
    elif update_type in ['long', 'int']:
        dt = datetime.strptime(update_time, '%Y-%m-%d')
        timestamp_ms = int(dt.timestamp() * 1000)
        time_filter = f"`{update_column}` >= {timestamp_ms}"
    else:
        time_filter = f"`{update_column}` >= '{update_time}'"
    
    if any(x in query.lower() for x in (' and ', ' or ', '(', ')')):
        return '(' + query + ') AND ' + time_filter
    else:
        return query + ' AND ' + time_filter    


def parse_filters(filter_file: str) -> TableFilters:
    """解析scan.yml文件"""
    with open(filter_file, 'r') as f:
        filters = yaml.safe_load(f)
    return TableFilters(**filters)


def parse_query_csv(csv_file: str) -> List[Dict]:
    """
    解析 query.csv 文件，支持通配符模式
    
    Args:
        csv_file: CSV 文件路径
        
    Returns:
        List[Dict]: 解析的表信息列表，可能包含通配符模式
    """
    if not os.path.exists(csv_file):
        get_run_logger().warning(f"CSV file does not exist: {csv_file}")
        return []
    
    try:
        df = pd.read_csv(csv_file)
        # 填充NaN值为空字符串
        df = df.fillna('')
        
        tables = []
        for _, row in df.iterrows():
            # 只处理有表名的行
            if not row['name'] or row['name'].strip() == '':
                continue
                
            table_info = {
                'name': str(row['name']).strip(),
                'tenant_column': str(row.get('tenant_column', '')).strip(),
                'tenant_type': str(row.get('tenant_type', '')).strip(),
                'tenant_indexed': bool(row.get('tenant_indexed', False)),
                'update_column': str(row.get('update_column', '')).strip(),
                'update_type': str(row.get('update_type', '')).strip(),
                'update_indexed': bool(row.get('update_indexed', False)),
                'query': str(row.get('query', '')).strip()
            }
            tables.append(table_info)
        
        print(f"Parsed {len(tables)} table patterns from {csv_file}")
        return tables
        
    except Exception as e:
        get_run_logger().error(f"Error parsing CSV file {csv_file}: {str(e)}")
        return []


@task(name='scan-missed-tables', log_prints=True, tags=['mysql', 'scan'])
async def scan_missed_tables(db_blk_name: str, tables: List[str], filters: TableFilters):
    """ 扫描未匹配的表，识别租户字段和更新字段 """
    # 1. 获取租户字段信息
    tenant_columns = await mysql_get_tenant_columns(db_blk_name, tables, filters.tenant_columns)
    
    if not tenant_columns:
        get_run_logger().warning(f"No tenant columns found for tables: {tables}")
        return

    # 2. 获取时间字段信息
    update_columns = await mysql_get_update_columns(db_blk_name, tables, filters.update_columns)

    results = []
    for table_name, tenant_column in tenant_columns.items():
        update_column = update_columns.get(table_name, {})
        tenant_name = tenant_column.get('name', '')
        results.append({
            'table_name': table_name,
            'tenant_column': tenant_name,
            'tenant_type': tenant_column.get('type', ''),
            'tenant_indexed': tenant_column.get('indexed', False),
            'update_column': update_column.get('name', ''),
            'update_type': update_column.get('type', ''),
            'update_indexed': update_column.get('indexed', False),
            'query': build_query_template(tenant_name, filters)
        })
    
    return results


@task(name='count-one-biz', log_prints=True, tags=['mysql', 'count'])
async def count_one_biz(
    db_blk_name: str, 
    biz_name: str, 
    ei: int, 
    update_time: Optional[str] = None) -> Dict[str, Dict]:
    """
    计算单个业务中所有表的行数，支持通配符匹配
    
    Args:
        db_blk_name: 数据库连接块名称
        biz_name: 业务名称
        ei: 企业ID
        update_time: 更新时间过滤器，格式为 '2024-01-01'
        
    Returns:
        Dict[str, Dict]: 表名到统计结果字典的映射，包含 count、query_hash 和 query
    """
    current_dir = os.path.dirname(os.path.abspath(__file__))
    biz_dir = biz_name.replace('-', '_')
    csv_file = os.path.join(current_dir, biz_dir, 'query.csv')
    scan_file = os.path.join(current_dir, biz_dir, 'scan.yml')
    
    # 1. 解析scan.yml获取过滤器
    filters = parse_filters(scan_file)
    
    # 2. 扫描数据库中的所有表
    all_tables = await mysql_scan_tables(db_blk_name, filters)
    if not all_tables:
        get_run_logger().warning(f"No tables found in database {db_blk_name}")
        return {}
    
    # 3. 解析query.csv中的表模式
    table_patterns = parse_query_csv(csv_file)
    if not table_patterns:
        get_run_logger().warning(f"Business {biz_name} has no table patterns to calculate")
        return {}
    
    # 4. 根据通配符模式匹配实际表名
    matched_tables, missed_tables = match_tables_with_patterns(all_tables, table_patterns)
    if not matched_tables and not missed_tables:
        get_run_logger().warning(f"No tables matched for business {biz_name}")
        return {}
    
    if missed_tables:
        found_tables = await scan_missed_tables(db_blk_name, missed_tables, filters)
        if found_tables:
            print(f"Found {len(found_tables)} missed tables, add to matched tables")
            matched_tables.extend(found_tables)
    names = sorted(set([t['name'] for t in matched_tables]))
    print(f"Matched {len(names)} tables: {names}")
    
    # 5. 并行计算所有匹配表的行数
    tasks = []
    for table_info in matched_tables:
        task = count_table_rows( db_blk_name, table_info, ei, update_time )
        tasks.append(task)
    
    # 6. 等待所有任务完成
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 7. 处理结果
    count_results, successful_counts, failed_counts = analyze_results(results)
    
    # 8. 创建报告
    create_count_artifacts(db_blk_name, biz_name, ei, count_results, successful_counts, failed_counts, update_time)
    
    print(f"Business {biz_name} count completed, successful: {len(successful_counts)}, failed: {len(failed_counts)}")
    return count_results


def analyze_results(results: List) -> Tuple[Dict, List, List]:
    count_results = {}
    successful_counts = []
    failed_counts = []
    
    for result in results:
        if isinstance(result, Exception):
            get_run_logger().error(f"Count task failed: {str(result)}")
            failed_counts.append(str(result))
        else:
            table_name, count, query, query_hash = result # type: ignore
            count_results[table_name] = { 'count': count, 'query_hash': query_hash, 'query': query }
            if count >= 0:
                successful_counts.append(f"{table_name}: {count} (query: {query}, hash: {query_hash})")
            else:
                failed_counts.append(f"{table_name}: query failed (hash: {query_hash})")
    return count_results, successful_counts, failed_counts


def create_count_artifacts(db_blk_name: str, biz_name: str, ei: int,
                          count_results: Dict[str, Dict],
                          successful_counts: List[str],
                          failed_counts: List[str],
                          update_time: Optional[str] = None):
    """
    创建统计报告工件
    """
    # Create table report
    table_data = []
    for table_name, result in count_results.items():
        count = result['count']
        query_hash = result['query_hash']
        status = "Success" if count >= 0 else "Failed"
        table_data.append({
            "Table Name": table_name,
            "Row Count": count if count >= 0 else "N/A",
            "Query Hash": query_hash,
            "Status": status
        })
    
    if table_data:
        create_table_artifact(
            key=f"count-{db_blk_name}-{biz_name}-{ei}",
            table=table_data,
            description=f"数据库 {db_blk_name} 业务 {biz_name} 企业 {ei} 的行数统计"
        )
    
    # Create detailed report
    total_tables = len(count_results)
    total_rows = sum(result['count'] for result in count_results.values() if result['count'] >= 0)
    success_count = len(successful_counts)
    fail_count = len(failed_counts)
    
    update_time_info = f"- **Update Time Filter**: {update_time}\n" if update_time else ""
    
    report = f"""# 数据库表行数统计报告

## 基本信息
- **数据库**: {db_blk_name}
- **业务**: {biz_name}
- **企业ID**: {ei}
{update_time_info}- **统计时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 统计结果
- **总表数**: {total_tables}
- **成功统计**: {success_count}
- **失败统计**: {fail_count}
- **总行数**: {total_rows:,}

## 成功统计的表
"""

    if successful_counts:
        for item in successful_counts:
            report += f"- {item}\n"
    else:
        report += "无\n"

    if failed_counts:
        report += "\n## 失败统计的表\n"
        for item in failed_counts:
            report += f"- {item}\n"

    create_markdown_artifact(
        key=f"count-report-{db_blk_name}-{biz_name}-{ei}",
        markdown=report,
        description=f"数据库 {db_blk_name} 业务 {biz_name} 企业 {ei} 的详细统计报告"
    )


@flow(name="mysql-count-many-biz", log_prints=True, 
      flow_run_name="count-{env}-{dialect}-{ei}", 
      timeout_seconds=600, 
      description="并行统计多个业务的数据库表行数")
async def count_many_biz(env: str, dialect: str, biz_names: List[str], ei: int, update_time: Optional[str] = None) -> Dict[str, Dict[str, Dict]]:
    """
    并行统计多个业务的表行数
    
    Args:
        env: 环境名称
        dialect: 数据库类型
        biz_names: 业务名称列表
        ei: 企业ID
        update_time: 更新时间过滤器，格式为 '2024-01-01'
        
    Returns:
        Dict[str, Dict[str, Dict]]: 业务名称到表统计结果映射的字典
    """
    # Create all count tasks
    count_tasks = []
    for biz in biz_names:
        db_blk_name = f"{env}-{dialect}-{biz}"
        print(f"Preparing count task: {db_blk_name}")
        
        with tags(env, dialect, db_blk_name, str(ei)):
            task = count_one_biz(db_blk_name, biz, ei, update_time)
            count_tasks.append((biz, task))
    
    # Execute all count tasks in parallel
    results = await asyncio.gather(*[task for _, task in count_tasks], return_exceptions=True)
    
    # Process results
    final_results = {}
    for i, (biz_name, _) in enumerate(count_tasks):
        result = results[i]
        if isinstance(result, Exception):
            get_run_logger().error(f"Business {biz_name} count failed: {str(result)}")
            final_results[biz_name] = {}
        else:
            print(f"Business {biz_name} count successful")
            final_results[biz_name] = result
    
    return final_results


@flow(name="mysql-count-one-biz", log_prints=True, 
      flow_run_name="count-{env}-{dialect}-{biz}-{ei}", 
      timeout_seconds=300, 
      description="统计单个业务的数据库表行数")
async def count_single_biz(env: str, dialect: str, biz: str, ei: int, update_time: Optional[str] = None) -> Dict[str, Dict]:
    """
    统计单个业务的表行数
    
    Args:
        env: 环境名称
        dialect: 数据库类型
        biz: 业务名称
        ei: 企业ID
        update_time: 更新时间过滤器，格式为 '2024-01-01'
        
    Returns:
        Dict[str, Dict]: 表名到统计结果字典的映射，包含 count、query_hash 和 query
    """
    db_blk_name = f"{env}-{dialect}-{biz}"
    
    with tags(env, dialect, db_blk_name, str(ei)):
        return await count_one_biz(db_blk_name, biz, ei, update_time)


def count_one():
    """测试统计单个业务的表行数"""
    env = 'firstshare'
    dialect = 'mysql'
    biz = 'mail'
    ei = 123456  # 示例企业ID
    update_time = None  # 可以设置为 '2024-01-01' 进行时间过滤
    asyncio.run(count_single_biz(env, dialect, biz, ei, update_time))


def count_many():
    """测试统计多个业务的表行数"""
    env = 'firstshare'
    dialect = 'mysql'
    biz_names = [
        'app-center',
        'call-center',
        'job-center',
        'mail',
        'online-consult',
        'open-app-pay',
        'open-message',
        'open-oauth',
        'open-qywx',
        'open-sail',
        'paas-crm-biz',
        'paas-template',
        'wechat-notice',
        'wechat-proxy',
        'wechat-union'
    ]
    ei = 123456  # 示例企业ID
    update_time = None  # 可以设置为 '2024-01-01' 进行时间过滤
    asyncio.run(count_many_biz(env, dialect, biz_names, ei, update_time))


if __name__ == '__main__':
    # 测试单个业务统计
    print("\nTesting count_one...")
    count_one()
    
    # 测试多个业务统计
    #print("\nTesting count_many...")
    #count_many()

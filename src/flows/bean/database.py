# -*- coding: utf-8 -*-

import re
from dataclasses import dataclass
from typing import Optional


@dataclass
class DbHost:
    """
    Database host configuration.
    """
    dialect: str
    host: str
    port: int
    user: str
    password: str
    database: str
    schema: Optional[str] = None

    def __str__(self):
        return f"{self.dialect}://{self.user}:***@{self.host}:{self.port}/{self.database}"

    def __repr__(self):
        return self.__str__()


@dataclass
class DbTable:
    """
    Database table configuration.
    """
    name: str
    column: Optional[str] = None
    type: Optional[str] = None
    scope: Optional[str] = None

    def __str__(self):
        return f"{self.name}.{self.column}:{self.type}"

    def __repr__(self):
        return self.__str__()


@dataclass
class TableFilters:
    """
    Table filters configuration.
    """
    tenant_columns: list[str]
    update_columns: list[str]
    include_names: list[str]
    exclude_names: list[str]
    include_names_regex: list[str]
    exclude_names_regex: list[str]
    ea_alias: list[str]
    ei_alias: list[str]

    def keep(self, name: str) -> bool:
        """
        Check if the table name matches the filters.
        """
        if self.include_names and name in self.include_names:
            return True
        if self.include_names_regex and any(re.search(regex, name) for regex in self.include_names_regex):
            return True
        if self.exclude_names and name in self.exclude_names:
            return False
        if self.exclude_names_regex and any(re.search(regex, name) for regex in self.exclude_names_regex):
            return False
        return True

    def get_name_alias(self, name: str) -> str:
        """
        Get the EA or EI alias for the table name.
        """
        if self.ea_alias and name in self.ea_alias:
            return 'ea'
        if self.ei_alias and name in self.ei_alias:
            return 'ei'
        if any(x in name.lower() for x in ['_ea', 'account']):
            return 'ea'
        if any(x in name.lower() for x in ['_ei', 'tenant_id']):
            return 'ei'
        return ''

@dataclass
class TableMeta:
    """
    Table metadata configuration.
    """
    name: str
    tenant_column: Optional[str] = None
    tenant_column_type: Optional[str] = None
    tenant_column_indexed: Optional[bool] = None
    update_column: Optional[str] = None
    update_column_type: Optional[str] = None
    update_column_indexed: Optional[bool] = None
    id_column: Optional[str] = None
    id_column_type: Optional[str] = None
    id_column_indexed: Optional[bool] = None
    id_column_auto_increment: Optional[bool] = None
    count: Optional[int] = None
    query: Optional[str] = None
    schema_hash: Optional[str] = None
    field_names: Optional[list[str]] = None

# -*- coding: utf-8 -*-

import datetime
import os

import urllib3
from botocore.config import Config
from prefect_aws import AwsCredentials

urllib3.disable_warnings()


def get_content_type(file_path: str) -> str:
    """
    根据文件路径获取对应的 ContentType
    
    Args:
        file_path: 文件路径
        
    Returns:
        str: 对应的 ContentType，默认为 'application/octet-stream'
    """
    mappings = {
        '.txt': 'text/plain',
        '.log': 'text/plain',
        '.sql': 'text/plain',
        '.csv': 'text/csv',
        '.html': 'text/html',
        '.css': 'text/css',
        '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        '.xls': 'application/vnd.ms-excel',
        '.doc': 'application/msword',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.ppt': 'application/vnd.ms-powerpoint',
        '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        '.odt': 'application/vnd.oasis.opendocument.text',
        '.odp': 'application/vnd.oasis.opendocument.presentation',
        '.ods': 'application/vnd.oasis.opendocument.spreadsheet',
        '.json': 'application/json',
        '.xml': 'application/xml',
        '.js': 'application/javascript',
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.gif': 'image/gif',
        '.pdf': 'application/pdf',
        '.zip': 'application/zip',
        '.tar': 'application/x-tar',
        '.gz': 'application/gzip'
    }
    
    # 获取文件扩展名
    file_extension = os.path.splitext(file_path)[1].lower()
    
    # 根据扩展名获取ContentType，默认为'application/octet-stream'
    return mappings.get(file_extension, 'application/octet-stream')


def upload_s3(src_file: str, dst_file: str, tags: str = '', expiry_days: int = 60, s3_bucket: str = "prefect", s3_blk_name: str = "prefect-s3"):
    """
    上传文件到 S3/MinIO 存储
    
    Args:
        src_file: 本地源文件路径
        dst_file: 目标文件路径（在存储桶中的路径）
        tags: 文件标签
        expiry_days: 文件过期时间（天）, 默认60天
        s3_bucket: 存储桶名称, 默认prefect
        s3_blk_name: 存储块名称, 默认prefect-s3
    """
    aws_credentials = AwsCredentials.load(s3_blk_name)
    params = aws_credentials.aws_client_parameters
    session = aws_credentials.get_boto3_session()
    s3 = session.client(
        service_name="s3",
        endpoint_url=params.endpoint_url,
        verify=params.verify,
        config=Config(connect_timeout=10, read_timeout=30)
    )
    
    # 获取文件的 ContentType
    content_type = get_content_type(src_file)
    expiry_time = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=expiry_days)

    try:
        with open(src_file, 'rb') as f:
            s3.put_object(Bucket=s3_bucket,
                          Key=dst_file,
                          Body=f,
                          ContentType=content_type,
                          Tagging=tags,
                          Expires=expiry_time)
    finally:
        s3.close()


if __name__ == "__main__":
    upload_s3("test.txt", "abc/test.txt", tags="env=firstshare&tenant=abc", expiry_days=12)
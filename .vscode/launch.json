{"python": "${workspaceFolder}/venv/bin/python", "version": "0.2.0", "configurations": [{"name": "debug-mysql-scan-tables", "type": "debugpy", "request": "launch", "module": "flows.mysql.scan", "console": "integratedTerminal", "cwd": "${workspaceFolder}/src"}, {"name": "debug-mysql-count-tables", "type": "debugpy", "request": "launch", "module": "flows.mysql.count", "console": "integratedTerminal", "cwd": "${workspaceFolder}/src"}]}
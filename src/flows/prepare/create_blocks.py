# -*- coding: utf-8 -*-

import argparse
import asyncio
import base64
import csv
from urllib.parse import urlparse

from prefect import get_client
from prefect_sqlalchemy import (AsyncDriver, ConnectionComponents,
                                SqlAlchemyConnector)


def get_driver(schema):
    """根据URL的schema选择合适的驱动"""
    drivers = {
        'mysql': AsyncDriver.MYSQL_ASYNCMY,
        'postgresql': AsyncDriver.POSTGRESQL_ASYNCPG,
    }
    return drivers.get(schema, AsyncDriver.SQLITE_AIOSQLITE)

def create_connector(row):
    """根据CSV行数据创建连接器，包含超时配置"""
    parsed = urlparse(row['url'])
    schema = parsed.scheme.lower()
    host = parsed.hostname
    port = parsed.port
    database = parsed.path.lstrip('/')

    # 解码base64密码
    password = base64.b64decode(row['base64pwd']).decode('utf-8')

    # 连接参数信息
    connection_info = {
        'username': row['username'],
        'password': password,
        'host': host,
        'database': database,
        'driver': get_driver(schema),
    }

    if schema == 'mysql':
        connection_info['query'] = {
            'charset': 'utf8mb4',
            'connect_timeout': '10',
            'read_timeout': '60'
        }

    if port:
        connection_info['port'] = port

    return SqlAlchemyConnector(
        connection_info=ConnectionComponents(**connection_info)
    )


async def create_concurrency_limit(tag, concurrency_limit):
    async with get_client() as client:
        limit_id = await client.create_concurrency_limit(
            tag=tag,
            concurrency_limit=concurrency_limit
        )
        return limit_id


async def process_csv(csv_file):
    """处理CSV文件并创建blocks"""
    print(f"processing csv file: {csv_file}")
    names = set()
    with open(csv_file, 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            schema_name = row['url'].split('://')[0]
            block_name = f"{row['env']}-{schema_name}-{row['biz']}"
            connector = create_connector(row)
            await connector.save(block_name, overwrite=True)
            print(f"created block: {block_name}")
            names.add(block_name)
    print(f"created {len(names)} blocks")
    for name in names:
        await create_concurrency_limit(name, 2)
        print(f"created concurrency limit for {name}, limit: 2")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='从CSV文件创建Prefect数据库连接块')
    parser.add_argument('csv_file', help='CSV文件路径')
    args = parser.parse_args()
    asyncio.run(process_csv(args.csv_file))

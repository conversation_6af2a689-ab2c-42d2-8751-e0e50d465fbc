# -*- coding: utf-8 -*-

from flows.bean import TransferState
from .state import MismatchFailure
from prefect import State
from prefect.artifacts import create_table_artifact


def extract_transfer_stat(s):
    """
    从不同类型的状态对象中提取传输统计信息
    
    Args:
        s: 状态对象，可以是 TransferState、MismatchFailure 或 State
        
    Returns:
        dict: 包含传输统计信息的字典
        
    Raises:
        ValueError: 当状态对象类型未知时
    """
    if isinstance(s, TransferState):
        return s.stat()
    elif isinstance(s, MismatchFailure):
        return s.data.stat()
    elif isinstance(s, (State,)):
        rs = s.result(raise_on_failure=False)
        if isinstance(rs, TransferState):
            return rs.stat()
        elif isinstance(rs, MismatchFailure):
            return rs.data.stat()
        else:
            raise ValueError(f"Unknown state: {type(rs)}, rs: {rs}")
    else:
        raise ValueError(f"Unknown state: {type(s)}, rs: {s}")


def create_flow_artifact(states: list, key: str = 'mongo-dts'):
    """
    创建 Prefect 流程工件，用于展示同步结果
    
    Args:
        states: 状态对象列表
        key: 工件的键名，默认为 'mongo-dts'
    """
    if states is None or len(states) == 0:
        return
    
    lines = [extract_transfer_stat(s) for s in states if s is not None]
    create_table_artifact(
        key=key.lower().replace('_', '-'),
        table=lines,
        description="# 同步结果"
    )
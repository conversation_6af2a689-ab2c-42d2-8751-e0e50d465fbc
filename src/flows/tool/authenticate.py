# -*- coding: utf-8 -*-

import re
from functools import lru_cache

from prefect import variables
from prefect.blocks.system import Secret

from flows.bean import DbHost, S3Config


@lru_cache(maxsize=100)
def get_db_auth(prefix: str) -> DbHost:
    """
    从 Prefect 变量和密钥中获取数据库认证信息
    
    Args:
        prefix: 数据库配置前缀，例如 'mg_org_data'
        
    Returns:
        DbHost: 数据库连接信息对象
        
    Raises:
        ValueError: 当URI格式无法解析时
    """
    # 从variables中获取user, host, port, db
    uri = variables.get(f"db_{prefix}")
    # 从secrets中获取password
    prefix2 = prefix.replace('_', '-')
    password = Secret.load(f"pwd-{prefix2}").get()
    
    # mongo://user@host:port/db
    match = re.compile(r"(?P<dialect>\w+)://(?P<user>[\w\-]+)@(?P<host>[\w\-.]+):(?P<port>\d+)/(?P<db>[\w\-]+)").match(uri)
    if match is None:
        match = re.compile(r"(?P<dialect>\w+)://(?P<host>[\w\-.]+):(?P<port>\d+)/(?P<db>[\w\-]+)").match(uri)
        if match is None:
            raise ValueError(f"无法解析{uri}，例如：mongodb://host:port/db")
        else:
            dialect, host, port, db = match.groups()
            print(f"{prefix} {dialect}://@{host}:{port}/{db}")
            return DbHost(dialect, host, int(port), None, password, db)
    else:
        dialect, user, host, port, db = match.groups()
        print(f"{prefix} {dialect}://{user}:***@{host}:{port}/{db}")
        return DbHost(dialect, host, int(port), user, password, db)


@lru_cache(maxsize=100)
def get_minio_auth(prefix: str = 'minio') -> S3Config:
    """
    从 Prefect 变量和密钥中获取 MinIO/S3 认证信息
    
    Args:
        prefix: MinIO配置前缀，默认为 'minio'
        
    Returns:
        S3Config: S3/MinIO连接配置对象
    """
    url = variables.get(f"{prefix}_endpoint_url")
    access_key = Secret.load(f"{prefix}-access-key-id").get()
    secret_key = Secret.load(f"{prefix}-secret-access-key").get()
    return S3Config(url, access_key, secret_key, 'cn-beijing-1')
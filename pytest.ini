[pytest]
# 测试文件匹配模式
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# 添加 src 目录到 Python 路径
pythonpath = src

# testcontainers 配置
env =
    RYUK_DISABLED=false
    RYUK_CONTAINER_IMAGE=reg.firstshare.cn/docker.io/testcontainers/ryuk:0.11.0
    SSHD_CONTAINER_IMAGE=reg.firstshare.cn/docker.io/testcontainers/sshd:1.2.0
    MINIO_CONTAINER_IMAGE=reg.firstshare.cn/docker.io/bitnami/minio:2025
    VNCRECORDER_CONTAINER_IMAGE=reg.firstshare.cn/docker.io/testcontainers/vnc-recorder:1.3.0
    TINYIMAGE_CONTAINER_IMAGE=reg.firstshare.cn/docker.io/alpine:3.17

# 测试报告格式
addopts = 
    --verbose
    --tb=short
    --showlocals
    --capture=no

# 测试标记定义
markers =
    slow: 标记耗时较长的测试
    integration: 标记集成测试
    unit: 标记单元测试
    mysql: 标记需要 MySQL 的测试
    async: 标记异步测试

# 测试覆盖率配置
[coverage:run]
source = flows
omit = 
    */test/*
    */__init__.py
    */migrations/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise NotImplementedError
    if __name__ == .__main__.:
    pass
    raise ImportError
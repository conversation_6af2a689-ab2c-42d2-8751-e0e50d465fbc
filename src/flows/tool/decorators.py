# -*- coding: utf-8 -*-

import time
from prefect import get_run_logger


def timing(func):
    """
    计时装饰器，用于记录函数执行时间
    如果执行时间超过60秒，会发出警告
    """
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            return func(*args, **kwargs)
        finally:
            cost = time.time() - start_time
            if cost > 60:
                get_run_logger().warning(f"{func.__name__}, 执行耗时：{cost:.2f}s，可能需要优化")
            else:
                get_run_logger().info(f"{func.__name__}, 执行耗时：{cost:.2f}s")

    return wrapper
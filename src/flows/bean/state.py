# -*- coding: utf-8 -*-

from dataclasses import dataclass
from datetime import datetime
from .database import DbHost


@dataclass
class SyncJob:
    tenant_ids: list[int]
    limit: int = 1000
    upsert: bool = False
    dry_run: bool = False


@dataclass
class TransferState:
    state: str
    src_db: DbHost
    dst_db: DbHost
    table: str
    src_count: int
    dst_count: int
    condition: str
    start_time: float
    end_time: float
    duration: float
    ei: int
    ea: str

    def src(self):
        if self.src_db:
            return self.src_db.host + '/' + self.src_db.database
        return ''

    def dst(self):
        if self.dst_db:
            return self.dst_db.host + '/' + self.dst_db.database
        return ''

    def cost(self):
        return f"{self.duration:.2f}"

    def start(self):
        return datetime.fromtimestamp(self.start_time).strftime("%Y-%m-%d %H:%M:%S")

    def end(self):
        return datetime.fromtimestamp(self.start_time).strftime("%Y-%m-%d %H:%M:%S")

    def stat(self):
        return {'ei': self.ei, 'ea': self.ea, 'src_num': self.src_count, 'dst_num': self.dst_count,
                'state': self.state, 'table': self.table,
                'query': self.condition, 'start_time': self.start(), 'end_time': self.end(), 'duration(s)': self.cost(),
                'src': self.src(), 'dst': self.dst()}
    
    def __str__(self):
        return f"TransferState: {self.state}, {self.table}, {self.src_count}, {self.dst_count}, {self.cost()}, {self.src()}, {self.dst()}, {self.start()}, {self.end()}, {self.condition}, {self.ea}"
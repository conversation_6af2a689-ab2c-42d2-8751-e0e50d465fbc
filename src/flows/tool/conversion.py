# -*- coding: utf-8 -*-

import pymongo
from functools import lru_cache
from urllib.parse import quote_plus
from prefect import get_run_logger
from .authenticate import get_db_auth


@lru_cache(maxsize=100)
def ei_to_ea(ei: int) -> str:
    """
    把数字类型的tenant-id转换为enterprise-account
    
    例如：1转换为fs
    
    Args:
        ei: 企业ID（Enterprise ID）
        
    Returns:
        str: 企业账户名（Enterprise Account），如果找不到则返回空字符串
    """
    if ei <= 0:
        return ''
    
    db = get_db_auth('mg_org_data')
    uri = f"mongodb://%s:%s@{db.host}:{db.port}/{db.database}" % (quote_plus(db.user), quote_plus(db.password))
    
    with pymongo.MongoClient(uri) as client:
        db2 = client[db.database]
        tbl = db2['EnterpriseInfoPo']
        one = tbl.find_one({'EI': ei})
        if one:
            ea = one['EA']
            get_run_logger().debug(f"ei={ei}对应的ea={ea}")
            return ea
        else:
            get_run_logger().error(f"找不到对应的EA信息，ei={ei}")
            return ''
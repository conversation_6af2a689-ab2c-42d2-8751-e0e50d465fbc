# -*- coding: utf-8 -*-

import argparse

from prefect_sqlalchemy import SqlAlchemyConnector


def read_block(block_name: str):
    block = SqlAlchemyConnector.load(block_name)
    print(f"Block type: {type(block)}")
    info = block.connection_info
    password = info.password.get_secret_value() if info.password else None
    print(f"Block data: {info}")
    print(f"Password (明文): {password}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='读取Prefect数据库连接块')
    parser.add_argument('block_name', help='块名称')
    args = parser.parse_args()
    read_block(args.block_name)
    
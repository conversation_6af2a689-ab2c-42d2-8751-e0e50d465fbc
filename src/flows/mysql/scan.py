# -*- coding: utf-8 -*-

import asyncio
import hashlib
import os
import re
from datetime import datetime
from typing import Dict, List, Tuple

import pandas as pd
import yaml
from prefect import flow, get_run_logger, tags, task
from prefect.artifacts import create_markdown_artifact, create_table_artifact
from prefect_sqlalchemy import SqlAlchemyConnector
from flows.bean import TableFilters
from flows.tool.storage import upload_s3


@task(name='upload-to-s3', log_prints=True, tags=['s3'])
def upload_to_s3(local_file: str, s3_file: str, expiry_days: int = 60):
    """ 上传文件到S3 """
    upload_s3(local_file, s3_file, expiry_days=expiry_days)
    print(f"Uploaded to {s3_file}, will expire in {expiry_days} days")


def column_data_sample(column_data: List, max_length: int) -> Dict[str, int]:
    """
    对单列数据进行采样和质量评估
    """
    total_samples = len(column_data)
    stats = {
        "total_samples": total_samples,
        "null_or_empty": 0,
        "whitespace_issues": 0,
        "special_ascii": 0,
        "length_exceeded": 0,
        "valid_data": 0
    }

    for value in column_data:
        # 检查空值或空字符串
        if value is None or (isinstance(value, str) and value.strip() == ""):
            stats["null_or_empty"] += 1
            continue

        # 转换为字符串进行检查
        str_value = str(value)

        # 检查是否包含空白字符（除了正常空格）
        if re.search(r'[\t\n\r\f\v]', str_value):
            stats["whitespace_issues"] += 1
            continue

        # 检查特殊ASCII字符（控制字符和不可打印字符）
        if re.search(r'[\x00-\x1f\x7f-\x9f]', str_value):
            stats["special_ascii"] += 1
            continue

        # 检查字符长度是否超过max_length
        if len(str_value) > max_length:
            stats["length_exceeded"] += 1
            continue

        # 通过所有检查的数据
        stats["valid_data"] += 1
    
    return stats


@task(name='evaluate-fields-quality', log_prints=True, tags = ['mysql'])
async def evaluate_fields_quality(
    db_blk_name: str,
    table_name: str,
    column_names: List[str],
    sample_size: int = 1000,
    max_length: int = 40) -> Dict[str, Tuple[int, Dict]]:
    """
    批量评估多个字段的数据质量并返回积分和详细信息

    Args:
        db_blk_name: 数据库连接名
        table_name: 表名
        column_names: 字段名列表
        sample_size: 采样数量，默认1000
        max_length: 字符串最大长度，默认40

    Returns:
        dict: {字段名: (积分, 详细信息字典)}
    """
    if not column_names:
        return {}

    connector = await SqlAlchemyConnector.load(db_blk_name) # type: ignore
    async with connector as conn:
        results = {}

        # 构建批量查询SQL，一次性获取所有字段的样本数据
        columns_sql = ', '.join([f'`{col}`' for col in column_names])
        sql = f"""
        SELECT {columns_sql}
        FROM `{table_name}`
        ORDER BY RAND()
        LIMIT {sample_size}
        """

        try:
            rows = await conn.fetch_all(sql) # type: ignore
        except Exception as e:
            get_run_logger().error(f"Failed to sample data from {table_name}: {e}")
            # 返回错误结果
            return {col: (0, {"error": str(e), "total_samples": 0}) for col in column_names}

        if not rows:
            return {col: (0, {"error": "No data found", "total_samples": 0}) for col in column_names}

        # 为每个字段进行质量评估
        for col_idx, column_name in enumerate(column_names):
            score = 100  # 初始满分100
            column_data = [row[col_idx] for row in rows]
            stats = column_data_sample(column_data, max_length)
            total_samples = stats["total_samples"]

            # 计算积分
            if total_samples > 0:
                # 每种问题按比例扣分
                null_penalty = (stats["null_or_empty"] / total_samples) * 30
                space_penalty = (stats["whitespace_issues"] / total_samples) * 50
                ascii_penalty = (stats["special_ascii"] / total_samples) * 80
                length_penalty = (stats["length_exceeded"] / total_samples) * 60

                score = max(0, score - null_penalty - space_penalty - ascii_penalty - length_penalty)

            stats["score"] = int(score)
            stats["valid_rate"] = int((stats["valid_data"] / total_samples) * 100) if total_samples > 0 else 0

            results[column_name] = (int(score), stats)
            print(f"Quality evaluation for {table_name}.{column_name}: score={stats['score']}, valid_rate={stats['valid_rate']}%")

        return results


@task(name='scan-tables', log_prints=True, tags=['mysql'])
async def mysql_scan_tables(db_blk_name: str, filters: TableFilters) -> List[str]:
    """ 查找当前库中有哪些表，并过滤需要的表名 """
    connector = await SqlAlchemyConnector.load(db_blk_name) # type: ignore
    async with connector as conn:
        sql = "SELECT table_name FROM information_schema.tables WHERE table_schema=DATABASE()"
        tables = [t[0] for t in await conn.fetch_all(sql)] # type: ignore
        keep_tables = [t for t in tables if filters.keep(t)]
        print(f"found {len(tables)} tables, keep {len(keep_tables)} tables")
        skipped = set(tables) - set(keep_tables)
        if len(skipped) > 0:
            get_run_logger().warning(f"skipped {len(skipped)} tables: {skipped}")
        return keep_tables


@task(name='get-table-rows', log_prints=True, tags=['mysql'])
async def mysql_get_table_rows(db_blk_name: str, tables: List[str]) -> Dict[str, int]:
    """ 获取每个表的预估记录行数（来自information_schema.tables） """
    connector = await SqlAlchemyConnector.load(db_blk_name) # type: ignore
    async with connector as conn:
        table_names = ','.join(["'" + name + "'" for name in tables])
        sql = f"""
        SELECT table_name, table_rows
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name IN ({table_names})
        """
        rows = await conn.fetch_all(sql) # type: ignore
        result = {row[0]: row[1] for row in rows}  # type: ignore
        return result


def get_name_score(name: str) -> int:
    """Get the score of a name based on predefined mappings."""
    mappings = {
        'ea': 10,
        'EA': 10,
        'ei': 10,
        'EI': 10,
        'fs_ea': 9,
        'fs_ei': 9,
        'enterprise_account': 9,
        'enterpriseAccount': 9,
        'enterprise_id': 9,
        'enterpriseId': 9,
        'tenant_id': 12,
        'tenantId': 11,
        'tenant': 9,
        'Ei': 5,
        'Ea': 5,
        'corp_id': 5,
        'corpId': 5,
        'fs_corp_id': 5,
        'upstream_ea': 4,
        'upstream_ei': 4,
        'downstream_ea': 3,
        'downstream_ei': 3,
    }
    return mappings.get(name, 0)


@task(name='get-tenant-columns', log_prints=True, tags=['mysql'])
async def mysql_get_tenant_columns(db_blk_name: str, tables: List[str], tenant_columns: List[str]) -> Dict[str, Dict]:
    """ 获取每个表的租户字段信息，包括数据质量评估 """
    connector = await SqlAlchemyConnector.load(db_blk_name) #type:ignore
    async with connector as conn:
        tenant_names = ','.join(["'" + name + "'" for name in tenant_columns])
        table_names = ','.join(["'" + name + "'" for name in tables])
        sql = f"""
        SELECT
            c.table_name,
            c.column_name,
            CASE
                WHEN c.data_type = 'int' THEN 'int'
                WHEN c.data_type = 'bigint' THEN 'long'
                ELSE 'string'
            END as field_type,
            COUNT(s.index_name) as index_count
        FROM information_schema.columns c
        LEFT JOIN information_schema.statistics s
            ON s.table_schema = c.table_schema
            AND s.table_name = c.table_name
            AND s.column_name = c.column_name
        WHERE c.table_schema = DATABASE()
        AND c.table_name IN ({table_names})
        AND c.column_name IN ({tenant_names})
        GROUP BY c.table_name, c.column_name, c.data_type
        """
        get_run_logger().debug(f"Executing SQL query: {sql}")
        rows = await conn.fetch_all(sql) # type: ignore

        # 先收集所有候选字段
        candidates = {}
        for row in rows:
            table_name, column_name, field_type, index_count = row # type: ignore
            if table_name not in candidates:
                candidates[table_name] = []
            candidates[table_name].append({
                'name': column_name,
                'type': field_type,
                'indexed': index_count > 0,
                'index_count': index_count
            })

        # 批量评估所有表的候选字段质量
        results = {}
        for table_name, options in candidates.items():
            if len(options) == 1:
                # 只有一个候选字段，直接使用
                results[table_name] = options[0]
            else:
                # 多个候选字段，批量评估后选择质量最高的
                field_names = [c['name'] for c in options]
                print(f"evaluating fields quality for table {table_name}: {field_names}")
                qualities = await evaluate_fields_quality(db_blk_name, table_name, field_names)

                for candidate in options:
                    field_name = candidate['name']
                    score, stats = qualities.get(field_name, (0, {"error": "Evaluation failed"}))
                    # 根据字段在索引中出现的次数加分
                    index_count = candidate['index_count']
                    if index_count == 1:
                        score += 8
                    elif index_count > 1:
                        score += index_count * 5
                    # 按照预置名称加分
                    score += get_name_score(field_name)
                    candidate['score'] = score
                    candidate['stats'] = stats

                # 按照分数排序
                options.sort(key=lambda x: x['score'], reverse=True)

                # 选择分数最高的字段
                choosed = options[0]
                results[table_name] = choosed

                others = ','.join([opt['name'] for opt in options[1:]])
                get_run_logger().warning(f"Table {table_name} has multiple tenant candidates, using {choosed['name']}, others: {others}")

        return results


@task(name='get-update-columns', log_prints=True, tags=['mysql'])
async def mysql_get_update_columns(db_blk_name: str, tables: List[str], update_columns: List[str]) -> Dict[str, Dict]:
    """ 获取每个表的时间字段信息，优先选择包含update、modify、modified的字段，并进行数据质量评估 """
    connector = await SqlAlchemyConnector.load(db_blk_name) # type: ignore
    async with connector as conn:
        update_names = ','.join(["'" + name + "'" for name in update_columns])
        table_names = ','.join(["'" + name + "'" for name in tables])
        sql = f"""
        SELECT
            c.table_name,
            c.column_name,
            CASE
                WHEN c.data_type = 'int' THEN 'int'
                WHEN c.data_type = 'bigint' THEN 'long'
                ELSE 'datetime'
            END as field_type,
            CASE WHEN COUNT(s.index_name) > 0 THEN 1 ELSE 0 END as indexed
        FROM information_schema.columns c
        LEFT JOIN information_schema.statistics s
            ON s.table_schema = c.table_schema
            AND s.table_name = c.table_name
            AND s.column_name = c.column_name
        WHERE c.table_schema = DATABASE()
        AND c.table_name IN ({table_names})
        AND c.column_name IN ({update_names})
        GROUP BY c.table_name, c.column_name, c.data_type
        ORDER BY c.table_name, c.column_name
        """
        get_run_logger().debug(f"Executing SQL query: {sql}")
        rows = await conn.fetch_all(sql) # type: ignore

        # 先收集所有候选字段
        candidates = {}
        for row in rows:
            table_name, column_name, field_type, indexed = row # type: ignore
            if table_name not in candidates:
                candidates[table_name] = []
            candidates[table_name].append({
                'name': column_name,
                'type': field_type,
                'indexed': bool(indexed),
                'score': 0 if 'create' in column_name.lower() else 10
            })

        results = {}
        for table_name, options in candidates.items():
            if len(options) == 0:
                results[table_name] = None
            elif len(options) == 1:
                results[table_name] = options[0]
            else:
                # 按照score倒序排序候选字段
                options.sort(key=lambda x: x['score'], reverse=True)
                choose = options[0]
                results[table_name] = choose
                others = ','.join([opt['name'] for opt in options[1:]])
                get_run_logger().debug(f"Multiple update_time candidates found for table {table_name}, using {choose['name']}, others: {others}")

        return results


@task(name='get-table-schema', log_prints=True, tags=['mysql'])
async def mysql_get_table_schema(db_blk_name: str, tables: List[str]) -> Dict[str, Dict]:
    """ 获取每个表的所有字段名，并计算hash值，同时检查是否有自增ID字段 """
    connector = await SqlAlchemyConnector.load(db_blk_name) # type: ignore
    async with connector as conn:
        table_names = ','.join(["'" + name + "'" for name in tables])
        sql = f"""
        SELECT
            table_name,
            GROUP_CONCAT(column_name ORDER BY column_name) as columns,
            GROUP_CONCAT(
                CASE
                    WHEN EXTRA = 'auto_increment' THEN column_name
                    ELSE NULL
                END
            ) as auto_increment_columns
        FROM information_schema.columns
        WHERE table_schema = DATABASE()
        AND table_name IN ({table_names})
        GROUP BY table_name
        """
        rows = await conn.fetch_all(sql) # type: ignore
        result = {}
        for row in rows:
            table_name, columns, auto_increment_columns = row # type: ignore
            if columns:
                get_run_logger().debug(f"table {table_name} has columns: {columns}")
                # 计算字段名排序后的hash值
                hash_value = hashlib.md5(columns.encode()).hexdigest()
                result[table_name] = {
                    'columns': columns,
                    'hash': hash_value[:8],
                    'auto_increment': auto_increment_columns.split(',')[0] if auto_increment_columns else ''
                }
        return result


@task(name='export-to-scan-excel', log_prints=True)
def export_to_scan_excel(
    db_blk_name: str,
    tables: List[str],
    row_counts: Dict[str, int],
    tenant_columns: Dict[str, Dict],
    update_columns: Dict[str, Dict],
    schema_hashes: Dict[str, Dict],
    filters: TableFilters
) -> str:
    """ 导出所有表信息到Excel文件 """
    data = []
    for table in tables:
        row = {
            'table_name': table,
            'row_count': row_counts.get(table, 0),
            'auto_increment': schema_hashes.get(table, {}).get('auto_increment', ''),
        }
        # 获取所有表字段名
        columns = schema_hashes.get(table, {}).get('columns', '').split(',')

        # 添加租户字段信息
        tenant_info = tenant_columns.get(table, {})
        tenant_column = tenant_info.get('name', '')
        row['tenant_column'] = tenant_column
        row['tenant_column_type'] = tenant_info.get('type', '')
        # 如果没有租户字段，索引列设置为空
        if len(tenant_info) == 0:
            row['tenant_column_indexed'] = ''
            names = [c for c in columns if any(x in c.lower() for x in ['tenant', '_ea', 'enterprise', 'corp'])]
            get_run_logger().warning(f"table {table} has no tenant column, maybe: {names}")
        else:
            row['tenant_column_indexed'] = index_or_not(tenant_info)

        # 添加时间字段信息
        update_info = update_columns.get(table, {})
        row['update_column'] = update_info.get('name', '')
        row['update_column_type'] = update_info.get('type', '')
        # 如果没有时间字段，索引列设置为空
        if len(update_info) == 0:
            row['update_column_indexed'] = ''
            names = [c for c in columns if any(x in c.lower() for x in ['create', 'gmt', 'time', 'update', 'modify', 'modified'])]
            get_run_logger().warning(f"table {table} has no update column, maybe: {names}")
        else:
            row['update_column_indexed'] = index_or_not(update_info)

        # 将schema_hash作为最后一列
        row['schema_hash'] = schema_hashes.get(table, {}).get('hash', '')
        
        # 添加过滤条件
        row['query'] = build_query_template(tenant_column, filters)

        data.append(row)

    # 创建DataFrame并导出到Excel
    df = pd.DataFrame(data)
    excel_file = f'scan-{db_blk_name}.xlsx'
    df.to_excel(excel_file, index=False)
    print(f"Exported to {excel_file}")
    return excel_file


def build_query_template(tenant_column: str, filters: TableFilters) -> str:
    """ 构建查询条件模板 """
    template = ''
    if tenant_column:
        alias = filters.get_name_alias(tenant_column)
        if alias:
            template = f"`{tenant_column}`=${alias}"
        else:
            template = f"`{tenant_column}`=$ea"
    return template


@task(name='export-to-query-csv', log_prints=True)
def export_to_query_csv(
    query_file: str,
    db_blk_name: str,
    tables: List[str],
    tenant_columns: Dict[str, Dict],
    update_columns: Dict[str, Dict],
    schema_hashes: Dict[str, Dict],
    filters: TableFilters
):
    """ 导出表信息到CSV文件中，预置过滤条件 """
    data = []
    loaded_hashes = {}
    for table in tables:
        tenant_info = tenant_columns.get(table, {})
        update_info = update_columns.get(table, {})
        tenant_column = tenant_info.get('name', '')
        tenant_type = tenant_info.get('type', '')
        update_name = update_info.get('name', '')
        schema_hash = schema_hashes.get(table, {}).get('hash')
        if schema_hash not in loaded_hashes:
            loaded_hashes[schema_hash] = [table]
        else:
            others = loaded_hashes[schema_hash]
            others.append(table)
            top = others[0]
            print(f"skip table {table} because it has the same schema_hash to {top}")
            continue
        
        query = build_query_template(tenant_column, filters)
        row = {
            'name': table,
            'tenant_column': tenant_column,
            'tenant_type': tenant_type,
            'tenant_indexed': index_or_not(tenant_info),
            'update_column': update_name,
            'update_type': update_info.get('type', ''),
            'update_indexed': index_or_not(update_info),
            'schema_hash': schema_hash,
            'query': query
        }
        data.append(row)

    if not data:
        get_run_logger().warning(f"No tables with tenant columns found for {db_blk_name}, csv file will be empty.")
    for row in data:
        names = row['schema_hash']
        if len(names) > 1:
            name = row['name']
            # 把name最后的数字换成星号
            name2 = re.sub(r'\d+$', '*', name)
            row['name'] = name2
    
    # 创建DataFrame并导出到CSV
    df = pd.DataFrame(data, columns=['name', 'tenant_column', 'tenant_type', 'tenant_indexed', 'update_column', 'update_type', 'update_indexed', 'query'])
    df.to_csv(query_file, index=False)
    print(f"Exported {len(data)} tables to {query_file}")


@task(name="parse-filters", log_prints=True)
def parse_filters(filter_file: str) -> TableFilters:
    with open(filter_file, 'r') as f:
        filters = yaml.safe_load(f)
    return TableFilters(**filters)


def index_or_not(info: Dict) -> str:
    if len(info) == 0:
        return ''
    return 'Y' if info.get('indexed', False) else 'N'


@task(name='create-scan-artifacts', log_prints=True, tags=['artifacts'])
def create_scan_artifacts(
    db_blk_name: str,
    tables: List[str],
    row_counts: Dict[str, int],
    tenant_columns: Dict[str, Dict],
    update_columns: Dict[str, Dict],
    schema_hashes: Dict[str, Dict],
    excel_file: str,
    s3_file: str,
    filters: TableFilters
) -> None:
    """ 创建扫描报告artifacts """
    logger = get_run_logger()

    # 1. 创建扫描概览markdown artifact
    total_tables = len(tables)
    total_rows = sum(row_counts.values())
    tables_with_tenant = len([t for t in tables if t in tenant_columns])
    tables_with_update = len([t for t in tables if t in update_columns])
    tables_with_auto_increment = len([t for t in tables if schema_hashes.get(t, {}).get('auto_increment')])

    overview_content = f"""
# 数据库扫描报告

## 基本信息
- **数据库**: {db_blk_name}
- **扫描时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **总表数**: {total_tables}
- **总记录数**: {total_rows:,}
- **包含租户字段的表**: {tables_with_tenant}
- **包含更新时间字段的表**: {tables_with_update}
- **包含自增ID的表**: {tables_with_auto_increment}

## 过滤规则
- **包含表名**: {filters.include_names if filters.include_names else '全部'}
- **排除表名**: {filters.exclude_names if filters.exclude_names else '无'}
- **包含正则**: {filters.include_names_regex if filters.include_names_regex else '无'}
- **排除正则**: {filters.exclude_names_regex if filters.exclude_names_regex else '无'}
- **租户字段**: {filters.tenant_columns}
- **时间字段**: {filters.update_columns}

## 输出文件
- **本地文件**: {excel_file}
- **S3文件**: {s3_file}
- **过期时间**: 60天

## 扫描统计
| 指标 | 数量 |
|------|------|
| 总表数 | {total_tables} |
| 总记录数 | {total_rows:,} |
| 有租户字段的表 | {tables_with_tenant} |
| 有时间字段的表 | {tables_with_update} |
| 有自增ID的表 | {tables_with_auto_increment} |
"""

    create_markdown_artifact(
        key=f"overview-{db_blk_name}",
        markdown=overview_content,
        description=f"数据库 {db_blk_name} 扫描概览"
    )

    # 2. 创建表详情表格artifact
    table_details = []
    for table in tables:
        tenant_info = tenant_columns.get(table, {})
        update_info = update_columns.get(table, {})
        schema_info = schema_hashes.get(table, {})

        # 创建有序字典，确保字段哈希值作为最后一列
        detail_row = {
            '表名': table,
            '记录数': row_counts.get(table, 0),
            '自增ID字段': schema_info.get('auto_increment', ''),
            '租户字段': tenant_info.get('name', ''),
            '租户字段类型': tenant_info.get('type', ''),
            '租户字段已索引': index_or_not(tenant_info),
            '时间字段': update_info.get('name', ''),
            '时间字段类型': update_info.get('type', ''),
            '时间字段已索引': index_or_not(update_info),
        }
        # 将字段哈希值作为最后一列
        detail_row['字段哈希值'] = schema_info.get('hash', '') if schema_info.get('hash') else ''
        table_details.append(detail_row)

    create_table_artifact(
        key=f"details-{db_blk_name}",
        table=table_details,
        description=f"数据库 {db_blk_name} 表详细信息"
    )

    # 3. 创建统计信息artifact
    stats_data = [
        {'统计项': '总表数', '数量': total_tables},
        {'统计项': '总记录数', '数量': f"{total_rows:,}"},
        {'统计项': '有租户字段的表', '数量': tables_with_tenant},
        {'统计项': '有时间字段的表', '数量': tables_with_update},
        {'统计项': '有自增ID的表', '数量': tables_with_auto_increment},
        {'统计项': '平均每表记录数', '数量': f"{total_rows // total_tables if total_tables > 0 else 0:,}"},
    ]

    create_table_artifact(
        key=f"stats-{db_blk_name}",
        table=stats_data,
        description=f"数据库 {db_blk_name} 统计信息"
    )

    logger.info(f"Created artifacts for {db_blk_name}: overview, details, and stats")


@flow(name="msql-scan-one-biz", log_prints=True, flow_run_name="scan-{db_blk_name}", timeout_seconds=60, description="扫描mysql数据库的表")
async def scan_one_biz(db_blk_name: str, scan_file: str):
    current_dir = os.path.dirname(os.path.abspath(__file__))
    scan_file = os.path.join(current_dir, scan_file)
    query_csv = os.path.join(os.path.dirname(scan_file), 'query.csv')
    filters = parse_filters(scan_file)
    with tags('mysql', db_blk_name):
        # 1. 获取并过滤表名
        tables = await mysql_scan_tables(db_blk_name, filters)
        if len(tables) == 0:
            get_run_logger().warning(f"no tables found for {db_blk_name}")
            return

        # 2. 获取表记录行数
        row_counts = await mysql_get_table_rows(db_blk_name, tables)

        # 3. 获取租户字段信息
        tenant_columns = await mysql_get_tenant_columns(db_blk_name, tables, filters.tenant_columns)

        # 4. 获取时间字段信息
        update_columns = await mysql_get_update_columns(db_blk_name, tables, filters.update_columns)

        # 5. 获取表结构hash值
        schema_hashes = await mysql_get_table_schema(db_blk_name, tables)

        # 6. 导出到Excel
        excel_file = export_to_scan_excel(db_blk_name, tables, row_counts, tenant_columns, update_columns, schema_hashes, filters)
        
        # 7. 导出表过滤信息到filter.csv
        export_to_query_csv(query_csv, db_blk_name, tables, tenant_columns, update_columns, schema_hashes, filters)

        # 8. 上传到S3
        s3_file = f"scan/scan-{db_blk_name}.xlsx"
        upload_to_s3(excel_file, s3_file)

        # 8. 创建扫描报告artifacts
        create_scan_artifacts(
            db_blk_name,
            tables,
            row_counts,
            tenant_columns,
            update_columns,
            schema_hashes,
            excel_file,
            s3_file,
            filters
        )

        print(f"Completed scanning {len(tables)} tables")


@flow(name="mysql-scan-many-biz", log_prints=True, flow_run_name="scan-{env}-{dialect}", timeout_seconds=600, description="扫描数据库表")
async def scan_many_biz(env: str, dialect: str, biz_names: List[str]):
    # 创建所有扫描任务
    scan_tasks = []
    for biz in biz_names:
        biz2 = biz.replace('-', '_')
        db_blk_name = env + '-' + dialect + '-' + biz
        print(f"preparing scan task for {db_blk_name}")
        scan_filters = biz2 + '/scan.yml'
        with tags(env, dialect, db_blk_name):
            # 创建异步任务但不立即执行
            task = scan_one_biz(db_blk_name, scan_filters)
            scan_tasks.append((db_blk_name, task))
    
    # 并行执行所有扫描任务
    results = await asyncio.gather(*[task for _, task in scan_tasks], return_exceptions=True)
    
    # 处理结果
    for i, (db_blk_name, _) in enumerate(scan_tasks):
        result = results[i]
        if isinstance(result, Exception):
            print(f"Failed to scan {db_blk_name}: {str(result)}")
            get_run_logger().error(f"Error scanning {db_blk_name}: {str(result)}")
        else:
            print(f"Successfully scanned {db_blk_name}")


def scan_one():
    env = 'firstshare'
    dialect = 'mysql'
    biz = 'mail'
    db_blk_name = env + '-' + dialect + '-' + biz
    asyncio.run(scan_one_biz(db_blk_name, biz.replace('-', '_') + '/scan.yml'))


def scan_many():
    env = 'firstshare'
    dialect = 'mysql'
    biz_names = [
        'app-center',
        'call-center',
        'job-center',
        'mail',
        'online-consult',
        'open-app-pay',
        'open-message',
        'open-oauth',
        'open-qywx',
        'open-sail',
        'paas-crm-biz',
        'paas-template',
        'wechat-notice',
        'wechat-proxy',
        'wechat-union'
    ]
    asyncio.run(scan_many_biz(env, dialect, biz_names))


if __name__ == "__main__":
    scan_many()
